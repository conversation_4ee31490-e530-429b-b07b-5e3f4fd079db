import React, { useState, useEffect } from "react";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import { useQueryClient } from "@tanstack/react-query";
import { Edit, Trash } from 'lucide-react';
import api from "@/lib/axios";
import useDataFetching from "@/hooks/useDataFetching";
import UserForm from "./UserForm";
import Confirm from "@/components/ui/Confirm";
import { useTranslation } from "react-i18next";

const UsersList = () => {
  const { t } = useTranslation();
  const [state, setState] = useState({
    page: 1,
    pageSize: 10,
    search: "",
    debouncedSearch: "",
    isModalOpen: false,
    isEditMode: false,
    currentGenre: null,
  });

  const queryClient = useQueryClient();

  const { data: usersData, isLoading, refetch } = useDataFetching({
    queryKey: ["usersList", state.page, state.pageSize, state.debouncedSearch],
    endPoint: `admin/users?page=${state.page}&per_page=${state.pageSize}&search=${state.debouncedSearch}`,
  });

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setState((prev) => ({ ...prev, debouncedSearch: state.search }));
    }, 500);

    return () => clearTimeout(delayDebounceFn);
  }, [state.search]);

  const handleModalOpen = (editMode = false, genre = null) => {
    setState((prev) => ({
      ...prev,
      isModalOpen: true,
      isEditMode: editMode,
      currentGenre: genre,
    }));
  };

  const handleModalClose = () => {
    setState((prev) => ({ ...prev, isModalOpen: false, currentGenre: null }));
  };

  const handleDeleteClick = async (userId) => {
    Confirm(async () => {
      try {
        await api.delete(`admin/users/${userId}`);
        queryClient.invalidateQueries("usersList");
      } catch (error) {
        console.error(t("usersList.deleteError"), error);
      }
    });
  };

  return (
    <>
      <DataTable
        title={t("usersList.title")}
        columns={[
          {
            Header: t("usersList.name"),
            accessor: "name",
          },
          {
            Header: t("usersList.email"),
            accessor: "email",
          },
          {
            Header: t("usersList.role"),
            accessor: "role",
          },
          {
            Header: t("usersList.status"),
            accessor: "is_active",
            Cell: ({ value }) => (
              <span
                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  value ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                }`}
              >
                {value ? t("usersList.active") : t("usersList.inactive")}
              </span>
            ),
          },
          {
            Header: t("usersList.action"),
            accessor: "id",
            Cell: ({ value, row }) => (
              <div className="flex justify-center">
                <button
                  className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-2 rounded-lg"
                  onClick={() => handleModalOpen(true, row.original)}
                >
                  <Edit size={16} />
                </button>
                {/* <button
                  className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-2 rounded-lg ml-2"
                  onClick={() => handleDeleteClick(value)}
                >
                  <Trash size={16} />
                </button> */}
              </div>
            ),
          },
        ]}
        data={usersData?.data?.data || []}
        fetchData={refetch}
        loading={isLoading}
        totalPages={usersData?.data?.total_pages || 1}
        currentPage={usersData?.data?.current_page || 1}
        pageSize={state.pageSize}
        onPageChange={(page) => setState((prev) => ({ ...prev, page }))} 
        onPageSizeChange={(pageSize) => setState((prev) => ({ ...prev, pageSize }))} 
        onSearch={(search) => setState((prev) => ({ ...prev, search }))} 
        buttonLabel={t("usersList.addUser")}
        onButtonClick={() => handleModalOpen(false)}
      />

      {state.isModalOpen && (
        <Modal
          activeModal={state.isModalOpen}
          onClose={handleModalClose}
          title={state.isEditMode ? t("usersList.editUser") : t("usersList.addUser")}
        >
          <UserForm
            userData={state.isEditMode ? state.currentGenre : {}}
            isEditMode={state.isEditMode}
            handleModalClose={handleModalClose}
          />
        </Modal>
      )}
    </>
  );
};

export default UsersList;
