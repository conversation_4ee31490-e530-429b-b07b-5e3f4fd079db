import React, { useState } from "react";
import FullscreenButton from "../components/FullscreenButton";
import IconButton from "../components/IconButton";
import Tooltip from "@/components/ui/Tooltip";
import { useDispatch, useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import { setTextToSpeech, setShowGallery, setNoteTakingLayer } from "../store/ebookSlice";
import { setScale, resetScale } from "../store/scaleSlice";
import TextToSpeechModal from "../components/TextToSpeechModal";
import AccessibilityButton from "../components/AccessibilityButton";
import Gallery from "./Gallery";
import BookmarkModal from "./BookmarkModal";
import useIsMobile from "../../../hooks/useIsMobile";

const BookToolbar = ({ onGeneratePDF, zoomIn, zoomOut, resetZoom, handleJumpToPage }) => {
  const isMobile = useIsMobile();
  const location = useLocation();
  const [isBookmarkModalOpen, setIsBookmarkModalOpen] = useState(false);
  const [isGalleryOpen, setIsGalleryOpen] = useState(false);
  const isLoggedIn = useSelector((state) => state.auth.isAuthenticated);
  const isTemplatePreview = location.pathname.includes("templates");

  const data = useSelector((state) => state.ebook?.ebookData);
  const [isTextToSpeechModalOpen, setIsTextToSpeechModalOpen] = useState(false);
  const dispatch = useDispatch();
  const isTextToSpeechEnabled = useSelector((state) => state.ebook.isTextToSpeechEnabled);
  const scale = useSelector((state) => state.scale.scale);
  const noteTakingLayer = useSelector((state) => state.ebook.noteTakingLayer);

  const handleZoomIn = () => dispatch(setScale(Math.min(scale + 0.1, 2)));
  const handleZoomOut = () => dispatch(setScale(Math.max(scale - 0.1, 0.5)));
  const handleResetZoom = () => dispatch(resetScale());

  const commonButtons = [
    { text: "Settings", icon: "mdi:settings", onClick: () => setIsTextToSpeechModalOpen(true) },
    { text: "Zoom In", icon: "mdi:magnify-plus", onClick: zoomIn },
    { text: "Zoom Out", icon: "mdi:magnify-minus", onClick: zoomOut },
    { text: "Reset Zoom", icon: "mdi:restore", onClick: resetZoom },
    ...(isTemplatePreview ? [] : [{ text: "Bookmark", icon: "mdi:bookmark", onClick: () => setIsBookmarkModalOpen(true) }]),
    ...(isLoggedIn
      ? [
        {
          text: "Notes",
          icon: "mdi:note",
          onClick: () => dispatch(setNoteTakingLayer(!noteTakingLayer)),
        },
      ]
      : []),
  ];

  const ebookSpecificButtons = [
    { 
      text: "Gallery", 
      icon: "mdi:image", 
      onClick: () => {
        setIsGalleryOpen(!isGalleryOpen);
        dispatch(setShowGallery(!isGalleryOpen));
      }
    },
    { text: "Listen", icon: isTextToSpeechEnabled ? "mdi:pause-circle" : "mdi:play-circle", onClick: () => dispatch(setTextToSpeech()) },
    // { text: "Download PDF", icon: "mdi:file-pdf", onClick: () => onGeneratePDF && onGeneratePDF() },
  ];

  const buttons = data?.book_type === "ebook" ? [...commonButtons, ...ebookSpecificButtons] : commonButtons;

  return (
    <>
      <div className={`flex ${isMobile ? "flex-row" : "flex-col"} justify-center gap-2`}>
        <Tooltip text="Fullscreen" position={isMobile ? "top" : "right"}>
          <FullscreenButton />
        </Tooltip>

        {buttons.map((button, index) => (
          <Tooltip key={index} text={button.text} position={isMobile ? "top" : "right"}>
            <IconButton icon={button.icon} text={button.text} onClick={button.onClick} />
          </Tooltip>
        ))}

        {data?.book_type === "ebook" && (
          <Tooltip text="Accessibility Settings" position={isMobile ? "top" : "right"}>
            <AccessibilityButton />
          </Tooltip>
        )}
      </div>
      <BookmarkModal handleJumpToPage={handleJumpToPage} isOpen={isBookmarkModalOpen} onClose={() => setIsBookmarkModalOpen(false)} />
      <TextToSpeechModal isOpen={isTextToSpeechModalOpen} handleClose={() => setIsTextToSpeechModalOpen(false)} />
      <Gallery 
        isOpen={isGalleryOpen}
        onClose={() => {
          setIsGalleryOpen(false);
          dispatch(setShowGallery(false));
        }}
      />
    </>
  );
};

export default BookToolbar;
